import {User} from "../Schema/user.js"
import mongoose from "mongoose"
export const getSampleData = (req, res) => {
  // res.json({ message: 'This is sample data from the API controller.' });
};

export const getCertificateData = async (req, res) => {
  try {
    const users = await User.find();
    res.status(200).json(users);
  } catch (error) {
    console.error("❌ Error fetching certificate data:", error.message);
    res.status(500).json({ error: "Server error while fetching certificate data" });
  }
};

// GET /api/certificates/:certificateId
export const getCertificateDataByCertificateId = async (req, res) => {
  try {
    const { certificateId } = req.params;
    console.log("🔍 Searching for certificate:", certificateId);

    // Try using Mongoose model first (with new field names)
    const userByNewField = await User.findOne({ certificateNo: certificateId });

    if (userByNewField) {
      console.log("✅ Certificate found via Mongoose:", userByNewField.Name);
      return res.status(200).json(userByNewField);
    }

    // Fallback: Use raw MongoDB collection for old field names
    const collection = mongoose.connection.collection('certificate-details');
    const allDocs = await collection.find({}).toArray();
    const user = allDocs.find(doc => doc["Certificate No."] === certificateId);

    if (!user) {
      return res.status(404).json({ error: "No user found with that certificate number." });
    }

    console.log("✅ Certificate found via fallback:", user.Name);
    res.status(200).json(user);
  } catch (error) {
    console.error("❌ Error fetching certificate data by ID:", error.message);

    // Check if it's a database connection error
    if (error.name === 'MongooseServerSelectionError') {
      return res.status(503).json({
        error: "Database connection unavailable. Please try again later.",
        details: "MongoDB connection issue"
      });
    }

    res.status(500).json({ error: "Server error while fetching certificate data" });
  }
};
